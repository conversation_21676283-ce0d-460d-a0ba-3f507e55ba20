import nodemailer, { SentMessageInfo } from 'nodemailer';

  const transporter = nodemailer.createTransport({
    host: 'smtp0001.neo.space', // Namecheap's SMTP server
    port: 587,
    secure: false, // true for 465, false for other ports like 587
    auth: {
      user: process.env.EMAIL_USER, // Your Namecheap email
      pass: process.env.EMAIL_PASSWORD, // Your Namecheap email password
    },
  });

export async function sendEmail({
  to,
  subject,
  html,
  from = process.env.EMAIL_USER
}: {
  to: string;
  subject: string;
  html: string;
  from?: string;
}): Promise<{ success: boolean; data?: SentMessageInfo; error?: Error | unknown }> {
  try {
    // Log the size of the email content
    console.log(`Email size: ${html.length} characters`);
    
    // Gmail clips messages larger than ~102KB
    if (html.length > 100000) {
      console.warn('Warning: Email content exceeds 100KB and may be clipped by Gmail');
    }
    
    const info = await transporter.sendMail({
      from,
      to,
      subject,
      html
    });
    
    return { success: true, data: info };
  } catch (error) {
    console.error('Error sending email:', error);
    return { success: false, error };
  }
}
