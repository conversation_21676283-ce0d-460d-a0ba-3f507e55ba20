"use client";

export const dynamic = "force-dynamic";
import { ProductsGridSkeleton } from "@/components/ProductSkeleton";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardFooter } from "@/components/ui/card";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import {
  Sheet,
  Sheet<PERSON>lose,
  Sheet<PERSON>ontent,
  SheetDes<PERSON>,
  Sheet<PERSON>ooter,
  She<PERSON><PERSON>eader,
  She<PERSON><PERSON><PERSON><PERSON>,
  She<PERSON><PERSON>rigger,
} from "@/components/ui/sheet";
import { Textarea } from "@/components/ui/textarea";
import { revalidateProducts } from "@/lib/actions";
import { fetchProducts as cachedFetchProducts } from "@/lib/cached-fetch";
import * as Tabs from "@radix-ui/react-tabs";
import {
  Check,
  Eye,
  Filter,
  Pencil,
  Plus,
  RefreshCw,
  Search,
  SlidersHorizontal,
  Trash2,
  Upload,
} from "lucide-react";
import Image from "next/image";
import Link from "next/link";
import { useRouter, useSearchParams } from "next/navigation";
import { Suspense, useEffect, useState } from "react";

interface Product {
  id: string;
  title: string[];
  short_description: string[];
  long_description: string[];
  price: number;
  promotional_price_percent?: number;
  sales_price_percent?: number;
  image_url: string;
  gallery_urls?: string[];
  created_at?: string;
  category_id?: string;
  category_name?: string;
  subcategory_id?: string;
  is_featured?: boolean;
}

interface Category {
  id: string;
  name: string;
}

function ManageProductsPage() {
  const [products, setProducts] = useState<Product[]>([]);
  const [categories, setCategories] = useState<Category[]>([]);
  const [loading, setLoading] = useState(true);
  const [isSheetOpen, setIsSheetOpen] = useState(false);
  const [productToEdit, setProductToEdit] = useState<Product | null>(null);
  const [productToDelete, setProductToDelete] = useState<Product | null>(null);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [searchQuery, setSearchQuery] = useState("");

  const [editForm, setEditForm] = useState({
    title: [""],
    shortDesc: [""],
    longDesc: [""],
    price: 0,
    promotionalPricePercent: "",
    salesPricePercent: "",
    categoryId: "",
    subcategoryId: "",
    imageUrl: "",
    galleryUrls: [] as string[],
    isFeatured: false,
  });
  const [editMainImagePreview, setEditMainImagePreview] = useState<string | null>(null);
  const [editGalleryPreviews, setEditGalleryPreviews] = useState<string[]>([]);
  const [subcategories, setSubcategories] = useState<Category[]>([]);

  const router = useRouter();
  const searchParams = useSearchParams();

  const selectedCategory = searchParams.get("category") || "";
  const sortBy = searchParams.get("sort") || "newest";

  useEffect(() => {
    // Fetch categories
    const fetchCategories = async () => {
      try {
        const res = await fetch("/api/categories", {
          cache: 'force-cache',
          next: { revalidate: 3600 } // Cache categories for 1 hour
        });
        if (res.ok) {
          const data = await res.json();
          setCategories(data.categories || []);
        }
      } catch (error) {
        console.error("Error fetching categories:", error);
      }
    };
    fetchCategories();
    fetchProducts(selectedCategory, sortBy);
    
  }, [selectedCategory, sortBy]);

  const fetchProducts = async (categoryId?: string | null, sort?: string | null) => {
    setLoading(true);
    try {
      // Use the cached fetch function
      const products = await cachedFetchProducts(categoryId || undefined, sort || undefined);
      setProducts(products);
    } catch (error) {
      console.error("Error fetching products:", error);
    } finally {
      setLoading(false);
    }
  };

  const handleCategoryChange = (categoryId: string) => {
    const params = new URLSearchParams(searchParams.toString());
    if (categoryId) {
      params.set("category", categoryId);
    } else {
      params.delete("category");
    }
    router.push(`/admin/manage-products${params.toString() ? `?${params.toString()}` : ""}`);
  };

  const handleSortChange = (value: string) => {
    const params = new URLSearchParams(searchParams.toString());
    if (value && value !== "newest") {
      params.set("sort", value);
    } else {
      params.delete("sort");
    }
    router.push(`/admin/manage-products${params.toString() ? `?${params.toString()}` : ""}`);
  };

  const resetFilters = () => {
    router.push("/admin/manage-products");
  };

  const handleEditProduct = (product: Product) => {
    setProductToEdit(product);
    setIsEditDialogOpen(true);
  };

  const handleDeleteProduct = (product: Product) => {
    setProductToDelete(product);
    setIsDeleteDialogOpen(true);
  };

  // Fetch subcategories when category changes
    useEffect(() => {
    if (editForm.categoryId) {
      fetchSubcategories(editForm.categoryId);
      setEditForm((prev) => ({
        ...prev,
        subcategoryId: "",
      }));
    } else {
      setSubcategories([]);
      setEditForm((prev) => ({
        ...prev,
        subcategoryId: "",
      }));
    }
  }, [editForm.categoryId]);


  // When opening the edit dialog, prefill the form
  useEffect(() => {
    if (productToEdit) {
      setEditForm({
        title: productToEdit.title || [""],
        shortDesc: productToEdit.short_description || [""],
        longDesc: productToEdit.long_description || [""],
        price: productToEdit.price || 0,
        promotionalPricePercent: productToEdit.promotional_price_percent?.toString() || "",
        salesPricePercent: productToEdit.sales_price_percent?.toString() || "",
        categoryId: productToEdit.category_id || "",
        subcategoryId: productToEdit.subcategory_id ? String(productToEdit.subcategory_id) : "",
        imageUrl: productToEdit.image_url || "",
        galleryUrls: productToEdit.gallery_urls || [],
        isFeatured:
        productToEdit.is_featured === true ||
        (typeof productToEdit.is_featured === "string" && productToEdit.is_featured === "true") ||
        (typeof productToEdit.is_featured === "number" && productToEdit.is_featured === 1),
      });
      setEditMainImagePreview(productToEdit.image_url || null);
      setEditGalleryPreviews(productToEdit.gallery_urls || []);
      // Always pass both category and subcategory to fetchSubcategories
      if (productToEdit.category_id) {
        fetchSubcategories(productToEdit.category_id, productToEdit.subcategory_id);
      }
    }
  }, [productToEdit]);

  // Updated fetchSubcategories
  const fetchSubcategories = async (categoryId: string, subcategoryIdToSet?: string) => {
    try {
      const res = await fetch(`/api/subcategories?categoryId=${categoryId}`);
      if (res.ok) {
        const data = await res.json();
        setSubcategories(data.subcategories || []);
        // If a subcategoryId is provided, set it only if it exists in the new list
        if (subcategoryIdToSet) {

          const found = (data.subcategories || []).find(
            (sc: { id: string }) => String(sc.id) === String(subcategoryIdToSet)
       );
          setEditForm((prev) => ({
            ...prev,
            subcategoryId: found ? String(subcategoryIdToSet) : "",
          }));
        }
      } else {
        setSubcategories([]);
        setEditForm((prev) => ({
          ...prev,
          subcategoryId: "",
        }));
      }
    } catch {
      setSubcategories([]);
      setEditForm((prev) => ({
        ...prev,
        subcategoryId: "",
      }));
    }
  };

  // Handlers for array fields
  const handleEditArrayChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>, field: "title" | "shortDesc" | "longDesc", idx: number) => {
    setEditForm((prev) => {
      const arr = [...prev[field]];
      arr[idx] = e.target.value;
      return { ...prev, [field]: arr };
    });
  };
  const handleAddEditArrayField = (field: "title" | "shortDesc" | "longDesc") => {
    setEditForm((prev) => ({ ...prev, [field]: [...prev[field], ""] }));
  };
  const handleRemoveEditArrayField = (field: "title" | "shortDesc" | "longDesc", idx: number) => {
    setEditForm((prev) => {
      const arr = [...prev[field]];
      arr.splice(idx, 1);
      return { ...prev, [field]: arr };
    });
  };

  // Handler for other fields
  const handleEditChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {
    const { name, value, type } = e.target;
    setEditForm((prev) => ({
      ...prev,
      [name]: type === "checkbox" ? (e.target as HTMLInputElement).checked : value,
    }));
  };

  // Image upload handlers (implement your upload logic as needed)
  const handleEditImageChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      const reader = new FileReader();
      reader.onload = (ev) => {
        setEditMainImagePreview(ev.target?.result as string);
        setEditForm((prev) => ({ ...prev, imageUrl: ev.target?.result as string }));
      };
      reader.readAsDataURL(file);
      // TODO: Upload to server and set imageUrl to the uploaded URL
    }
  };
  const handleEditGalleryChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(e.target.files || []);
    const previews: string[] = [];
    files.forEach((file) => {
      const reader = new FileReader();
      reader.onload = (ev) => {
        previews.push(ev.target?.result as string);
        setEditGalleryPreviews((prev) => [...prev, ev.target?.result as string]);
        setEditForm((prev) => ({
          ...prev,
          galleryUrls: [...prev.galleryUrls, ev.target?.result as string],
        }));
      };
      reader.readAsDataURL(file);
      // TODO: Upload to server and set galleryUrls to the uploaded URLs
    });
  };
  const handleRemoveEditGalleryImage = (idx: number) => {
    setEditGalleryPreviews((prev) => prev.filter((_, i) => i !== idx));
    setEditForm((prev) => ({
      ...prev,
      galleryUrls: prev.galleryUrls.filter((_, i) => i !== idx),
    }));
  };

  // Update submit handler to use editForm
  const submitProductUpdate = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    if (!productToEdit) return;
    setIsSubmitting(true);
    try {
      const response = await fetch(`/api/products/${productToEdit.id}`, {
        method: "PUT",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          title: editForm.title,
          short_description: editForm.shortDesc,
          long_description: editForm.longDesc,
          price: Number(editForm.price),
          promotional_price_percent: editForm.promotionalPricePercent ? Number(editForm.promotionalPricePercent) : null,
          sales_price_percent: editForm.salesPricePercent ? Number(editForm.salesPricePercent) : null,
          category_id: editForm.categoryId,
          subcategory_id: editForm.subcategoryId,
          image_url: editForm.imageUrl,
          gallery_urls: editForm.galleryUrls,
          is_featured: editForm.isFeatured,
        }),
      });
      if (!response.ok) throw new Error("Failed to update product");
      // Update local state as needed...
      setIsEditDialogOpen(false);
      // Optionally refetch products
      fetchProducts();
    } catch (error) {
      console.error("Error updating product:", error);
    } finally {
      setIsSubmitting(false);
    }
  };

  const confirmDeleteProduct = async () => {
    if (!productToDelete) return;

    setIsSubmitting(true);

    try {
      const response = await fetch(`/api/products/${productToDelete.id}`, {
        method: "DELETE",
      });

      if (!response.ok) {
        throw new Error("Failed to delete product");
      }

      setProducts(products.filter((p) => p.id !== productToDelete.id));
      setIsDeleteDialogOpen(false);
    } catch (error) {
      console.error("Error deleting product:", error);
    } finally {
      setIsSubmitting(false);
    }
  };

  const filteredProducts = products.filter((product) => {
    if (!searchQuery) return true;
    const title = product.title[0]?.toLowerCase() || "";
    const description = product.short_description[0]?.toLowerCase() || "";
    const query = searchQuery.toLowerCase();
    return title.includes(query) || description.includes(query);
  });

  // Add refresh function
  const refreshData = async () => {
    try {
      setLoading(true);
      
      // Revalidate products data
      await revalidateProducts();
      
      // Fetch fresh data
      await fetchProducts(selectedCategory, sortBy);
      
    } catch (error) {
      console.error("Error refreshing products:", error);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="container mx-auto px-4 py-8 max-w-7xl">
      <div className="bg-white rounded-xl shadow-sm border p-6 mb-8">
        <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4 mb-6">
          <div>
            <h1 className="text-3xl font-bold bg-gradient-to-r from-gray-900 to-gray-600 bg-clip-text text-transparent">
              Product Management
            </h1>
            <p className="text-gray-500 mt-1">Manage your product inventory</p>
          </div>
          <div className="flex gap-2">
            <Button onClick={refreshData} variant="outline" size="sm" disabled={loading}>
              <RefreshCw className={`h-4 w-4 mr-2 ${loading ? "animate-spin" : ""}`} />
              Refresh
            </Button>
            <Button asChild className="bg-gradient-to-r from-violet-600 to-indigo-600 hover:from-violet-700 hover:to-indigo-700">
              <Link prefetch={true} href="/admin/add-product">
                <Plus className="h-4 w-4 mr-2" />
                Add New Product
              </Link>
            </Button>
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-[1fr_auto] gap-4 mb-6">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
            <Input
              placeholder="Search products..."
              className="pl-10 bg-gray-50 border-gray-200"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
            />
          </div>
          <div className="flex gap-2">
            <Sheet onOpenChange={setIsSheetOpen}>
              <SheetTrigger asChild>
                <Button variant="outline" className="flex items-center gap-2">
                  <Filter className="h-4 w-4" />
                  <span className="hidden sm:inline">Filter</span>
                  {selectedCategory && (
                    <Badge className="ml-1 bg-violet-100 text-violet-800 hover:bg-violet-200">1</Badge>
                  )}
                </Button>
              </SheetTrigger>
              <SheetContent>
                <SheetHeader>
                  <SheetTitle>Filter Products</SheetTitle>
                  <SheetDescription>Filter products by category</SheetDescription>
                </SheetHeader>
                <div className="py-6">
                  <RadioGroup
                    key={`${isSheetOpen}-${selectedCategory}`}
                    value={selectedCategory}
                    onValueChange={handleCategoryChange}
                  >
                    <div className="space-y-2">
                      <div className="flex items-center space-x-2">
                        <RadioGroupItem value="" id="all" />
                        <Label htmlFor="all">All Categories</Label>
                      </div>
                      {categories.map((category) => (
                        <div key={category.id} className="flex items-center space-x-2">
                          <RadioGroupItem value={String(category.id)} id={String(category.id)} />
                          <Label htmlFor={String(category.id)}>{category.name}</Label>
                        </div>
                      ))}
                    </div>
                  </RadioGroup>
                </div>
                <SheetFooter>
                  <SheetClose asChild>
                    <Button variant="outline" onClick={resetFilters}>
                      Reset
                    </Button>
                  </SheetClose>
                </SheetFooter>
              </SheetContent>
            </Sheet>

            <Popover>
              <PopoverTrigger asChild>
                <Button variant="outline" className="flex items-center gap-2">
                  <SlidersHorizontal className="h-4 w-4" />
                  <span className="hidden sm:inline">Sort</span>
                </Button>
              </PopoverTrigger>
              <PopoverContent className="w-56">
                <div className="space-y-2">
                  <h4 className="font-medium">Sort by</h4>
                  <RadioGroup value={sortBy} onValueChange={handleSortChange}>
                    <div className="space-y-2">
                      <div className="flex items-center space-x-2">
                        <RadioGroupItem value="newest" id="newest" />
                        <Label htmlFor="newest">Newest</Label>
                      </div>
                      <div className="flex items-center space-x-2">
                        <RadioGroupItem value="price_asc" id="price_asc" />
                        <Label htmlFor="price_asc">Price: Low to High</Label>
                      </div>
                      <div className="flex items-center space-x-2">
                        <RadioGroupItem value="price_desc" id="price_desc" />
                        <Label htmlFor="price_desc">Price: High to Low</Label>
                      </div>
                    </div>
                  </RadioGroup>
                </div>
              </PopoverContent>
            </Popover>
          </div>
        </div>

        <Tabs.Root defaultValue="grid" className="mb-6">
          <div className="flex items-center justify-between">
            <Tabs.List className="bg-gray-100 rounded-lg p-1 flex gap-2 shadow-sm">
              <Tabs.Trigger
                value="grid"
                className="px-4 py-2 rounded-md font-medium transition-colors data-[state=active]:bg-white data-[state=active]:shadow data-[state=active]:text-gray-900 data-[state=inactive]:text-gray-500"
              >
                Grid View
              </Tabs.Trigger>
              <Tabs.Trigger
                value="list"
                className="px-4 py-2 rounded-md font-medium transition-colors data-[state=active]:bg-white data-[state=active]:shadow data-[state=active]:text-gray-900 data-[state=inactive]:text-gray-500"
              >
                List View
              </Tabs.Trigger>
            </Tabs.List>
            {/* <div className="flex items-center gap-2 text-sm text-gray-500">
              <span>Show archived</span>
              <Switch id="archived" />
            </div> */}
          </div>

          <Tabs.Content value="grid" className="mt-4">
            {loading ? (
              <ProductsGridSkeleton />
            ) : (
              <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
                {filteredProducts.length === 0 ? (
                  <div className="col-span-full text-center py-12">
                    <div className="mx-auto w-24 h-24 bg-gray-100 rounded-full flex items-center justify-center mb-4">
                      <Search className="h-10 w-10 text-gray-400" />
                    </div>
                    <h3 className="text-xl font-medium text-gray-600">No products found</h3>
                    <p className="mt-2 text-gray-500">Try adjusting your filters or search query.</p>
                  </div>
                ) : (
                  filteredProducts.map((product: Product) => (
                    <Card
                      key={product.id}
                      className="group overflow-hidden border-gray-200 h-full flex flex-col transition-all duration-200 hover:shadow-md hover:border-gray-300 hover:-translate-y-1"
                    >
                      <div className="relative aspect-square w-full overflow-hidden bg-gray-100">
                         <Link href={`/products/${product.id}`} prefetch={true}>
                        <Image
                          src={
                            product.image_url && product.image_url.trim() !== ""
                              ? product.image_url
                              : "/placeholder.svg?height=400&width=400"
                          }
                          alt={Array.isArray(product.title) && product.title.length > 0 ? product.title[0] : "Product"}
                          fill
                          sizes="(max-width: 640px) 100vw, (max-width: 768px) 50vw, (max-width: 1024px) 33vw, 25vw"
                          className="object-cover cursor-pointer"
                        />
                        </Link>
                        <div className="absolute inset-0 bg-black/0 group-hover:bg-black/10 transition-colors duration-200 pointer-events-none"></div>
                      
                         
                        {product.promotional_price_percent && (
                          <div className="absolute top-3 left-3">
                            <Badge className="bg-red-500 hover:bg-red-600">
                              {product.promotional_price_percent}% OFF
                            </Badge>
                          </div>
                        )}
                      </div>
                      <CardContent className="p-5 flex flex-col flex-grow">
                        {product.category_name && (
                          <Badge className="mb-2 self-start text-xs font-normal text-gray-600">
                            {product.category_name}
                          </Badge>
                        )}
                        <h3 className="font-semibold text-lg mb-1 line-clamp-1 group-hover:text-violet-700 transition-colors">
                          {Array.isArray(product.title) && product.title.length > 0 ? product.title[0] : "Product"}
                        </h3>
                        <p className="text-gray-500 text-sm mb-2 line-clamp-2">
                          {Array.isArray(product.short_description) && product.short_description.length > 0
                            ? product.short_description[0]
                            : ""}
                        </p>
                      </CardContent>
                      <CardFooter className="px-5 pb-5 pt-0 flex items-center justify-between mt-auto">
                        <div>
                          <p className="font-bold text-lg text-violet-700">${Number(product.price).toFixed(2)}</p>
                          {product.sales_price_percent && (
                            <p className="text-xs text-gray-500 line-through">
                              ${Number(product.price / (1 - product.sales_price_percent / 100)).toFixed(2)}
                            </p>
                          )}
                        </div>
                        <div className="flex gap-2">
                          <Button
                            variant="outline"
                            size="sm"
                            className="h-9 border-gray-200 hover:bg-violet-50 hover:text-violet-700 hover:border-violet-200"
                            onClick={() => handleEditProduct(product)}
                            title="Edit"
                          >
                            <Pencil className="h-4 w-4" />
                          </Button>
                          <Button asChild className="h-9 bg-violet-600 hover:bg-violet-700 text-white" title="View">
                            <Link prefetch={true} href={`/products/${product.id}`}>
                              <Eye className="h-4 w-4" />
                            </Link>
                          </Button>
                          <Button
                            variant="destructive"
                            size="sm"
                            className="h-9 bg-red-600 hover:bg-red-700 text-white"
                            onClick={() => handleDeleteProduct(product)}
                            title="Delete"
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>
                      </CardFooter>
                    </Card>
                  ))
                )}
              </div>
            )}
          </Tabs.Content>

          <Tabs.Content value="list" className="mt-4">
            <div className="border rounded-lg overflow-hidden">
              <div className="grid grid-cols-[1fr_2fr_1fr_1fr_auto] bg-gray-50 p-4 border-b font-medium text-sm text-gray-500">
                <div>Image</div>
                <div>Product</div>
                <div>Category</div>
                <div>Price</div>
                <div>Actions</div>
              </div>
              {loading ? (
                <div className="p-8 text-center">Loading...</div>
              ) : filteredProducts.length === 0 ? (
                <div className="p-8 text-center text-gray-500">No products found</div>
              ) : (
                filteredProducts.map((product) => (
                  <div
                    key={product.id}
                    className="grid grid-cols-[1fr_2fr_1fr_1fr_auto] items-center p-4 border-b hover:bg-gray-50 transition-colors"
                  >
                    <div className="relative h-16 w-16 rounded-md overflow-hidden bg-gray-100">
                      <Link href={`/products/${product.id}`} prefetch={true}>
                        <Image
                          src={
                            product.image_url && product.image_url.trim() !== ""
                              ? product.image_url
                              : "/placeholder.svg?height=400&width=400"
                          }
                          alt={Array.isArray(product.title) && product.title.length > 0 ? product.title[0] : "Product"}
                          fill
                          className="object-cover cursor-pointer"
                        />
                      </Link>
                    </div>
                    <div>
                      <h3 className="font-medium text-gray-900">
                        {Array.isArray(product.title) && product.title.length > 0 ? product.title[0] : "Product"}
                      </h3>
                      <p className="text-sm text-gray-500 line-clamp-1">
                        {Array.isArray(product.short_description) && product.short_description.length > 0
                          ? product.short_description[0]
                          : ""}
                      </p>
                    </div>
                    <div>
                      {product.category_name ? (
                        <Badge className="font-normal">
                          {product.category_name}
                        </Badge>
                      ) : (
                        <span className="text-gray-400">—</span>
                      )}
                    </div>
                    <div className="font-medium">${Number(product.price).toFixed(2)}</div>
                    <div className="flex gap-2 justify-end">
                      <Button
                        variant="ghost"
                        size="sm"
                        className="h-8 w-8 p-0 text-gray-500 hover:text-violet-700"
                        onClick={() => handleEditProduct(product)}
                      >
                        <Pencil className="h-4 w-4" />
                        <span className="sr-only">Edit</span>
                      </Button>
                      <Button
                        variant="ghost"
                        size="sm"
                        className="h-8 w-8 p-0 text-gray-500 hover:text-red-600"
                        onClick={() => handleDeleteProduct(product)}
                      >
                        <Trash2 className="h-4 w-4" />
                        <span className="sr-only">Delete</span>
                      </Button>
                      <Button
                        asChild
                        variant="ghost"
                        size="sm"
                        className="h-8 w-8 p-0 text-gray-500 hover:text-violet-700"
                      >
                        <Link prefetch={true} href={`/products/${product.id}`}>
                          <Eye className="h-4 w-4" />
                          <span className="sr-only">View</span>
                        </Link>
                      </Button>
                    </div>
                  </div>
                ))
              )}
            </div>
          </Tabs.Content>
        </Tabs.Root>
      </div>

      {/* Edit Product Dialog */}
      <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <DialogContent className="sm:max-w-[600px] max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle className="text-xl">Edit Product</DialogTitle>
            <DialogDescription>Update all product details below.</DialogDescription>
          </DialogHeader>
          <form onSubmit={submitProductUpdate}>
            <div className="grid gap-5 py-4">
              {/* Title array */}
              <div className="space-y-2">
                <Label>Name Product</Label>
                {editForm.title.map((val, idx) => (
                  <div key={idx} className="flex gap-2 mb-2">
                    <Input
                      name={`title-${idx}`}
                      placeholder={`Title ${idx + 1}`}
                      value={val}
                      onChange={(e) => handleEditArrayChange(e, "title", idx)}
                      required={idx === 0}
                      className="bg-gray-100"
                    />
                    {editForm.title.length > 1 && (
                      <Button type="button" variant="destructive" onClick={() => handleRemoveEditArrayField("title", idx)}>-</Button>
                    )}
                  </div>
                ))}
                <Button type="button" variant="outline" size="sm" onClick={() => handleAddEditArrayField("title")}>
                  + Add Title
                </Button>
              </div>
              {/* Short Desc array */}
              <div className="space-y-2">
                <Label>Short Description</Label>
                {editForm.shortDesc.map((val, idx) => (
                  <div key={idx} className="flex gap-2 mb-2">
                    <Input
                      name={`shortDesc-${idx}`}
                      placeholder={`Short Description ${idx + 1}`}
                      value={val}
                      onChange={(e) => handleEditArrayChange(e, "shortDesc", idx)}
                      required={idx === 0}
                      className="bg-gray-100"
                    />
                    {editForm.shortDesc.length > 1 && (
                      <Button type="button" variant="destructive" onClick={() => handleRemoveEditArrayField("shortDesc", idx)}>-</Button>
                    )}
                  </div>
                ))}
                <Button type="button" variant="outline" size="sm" onClick={() => handleAddEditArrayField("shortDesc")}>
                  + Add Short Desc
                </Button>
              </div>
              {/* Long Desc array */}
              <div className="space-y-2">
                <Label>Description Product</Label>
                {editForm.longDesc.map((val, idx) => (
                  <div key={idx} className="flex gap-2 mb-2">
                    <Textarea
                      name={`longDesc-${idx}`}
                      placeholder={`Long Description ${idx + 1}`}
                      value={val}
                      onChange={(e) => handleEditArrayChange(e, "longDesc", idx)}
                      required={idx === 0}
                      className="min-h-[80px] bg-gray-100"
                    />
                    {editForm.longDesc.length > 1 && (
                      <Button type="button" variant="destructive" onClick={() => handleRemoveEditArrayField("longDesc", idx)}>-</Button>
                    )}
                  </div>
                ))}
                <Button type="button" variant="outline" size="sm" onClick={() => handleAddEditArrayField("longDesc")}>
                  + Add Long Desc
                </Button>
              </div>
              {/* Image upload */}
              <div>
                <Label>Product Image</Label>
                <div className="border rounded-lg p-4 bg-blue-50 flex flex-col items-center justify-center min-h-[180px] relative">
                  {editMainImagePreview ? (
                    <>
                      <Image
                        src={editMainImagePreview}
                        alt="Product preview"
                        width={120}
                        height={120}
                        className="object-contain max-h-[120px]"
                      />
                      <label
                        htmlFor="edit-image"
                        className="absolute bottom-4 right-4 bg-white rounded-full p-2 shadow cursor-pointer"
                      >
                        <Upload className="h-5 w-5" />
                        <input
                          id="edit-image"
                          name="image"
                          type="file"
                          accept="image/*"
                          onChange={handleEditImageChange}
                          className="hidden"
                        />
                      </label>
                    </>
                  ) : (
                    <label className="flex flex-col items-center justify-center gap-2 cursor-pointer w-full h-full">
                      <div className="bg-gray-100 rounded-full p-3">
                        <Upload className="h-6 w-6 text-gray-500" />
                      </div>
                      <span className="text-sm text-gray-500">Upload main product image</span>
                      <input
                        id="edit-image"
                        name="image"
                        type="file"
                        accept="image/*"
                        onChange={handleEditImageChange}
                        className="hidden"
                      />
                    </label>
                  )}
                </div>
              </div>
              {/* Gallery images */}
              <div>
                <Label>Gallery Images</Label>
                <div className="flex gap-2 mt-2 overflow-x-auto pb-2">
                  {editGalleryPreviews.map((preview, index) => (
                    <div key={index} className="border rounded-md h-16 w-16 flex-shrink-0 overflow-hidden relative">
                      <Image
                        src={preview}
                        alt={`Gallery image ${index + 1}`}
                        width={64}
                        height={64}
                        className="object-cover w-full h-full"
                      />
                      <button
                        type="button"
                        className="absolute top-1 right-1 bg-white rounded-full p-1 shadow text-red-500"
                        onClick={() => handleRemoveEditGalleryImage(index)}
                      >
                        ×
                      </button>
                    </div>
                  ))}
                  <label className="border rounded-md h-16 w-16 flex-shrink-0 flex items-center justify-center cursor-pointer bg-blue-50">
                    <Plus className="h-5 w-5 text-gray-400" />
                    <input
                      name="galleryImages"
                      type="file"
                      accept="image/*"
                      multiple
                      onChange={handleEditGalleryChange}
                      className="hidden"
                    />
                  </label>
                </div>
              </div>
              {/* Featured */}
              <div className="flex items-center gap-2 mt-2">
                <input
                  type="checkbox"
                  id="edit-isFeatured"
                  name="isFeatured"
                  checked={editForm.isFeatured}
                  onChange={handleEditChange}
                  className="h-4 w-4 accent-blue-600"
                />
                <Label htmlFor="edit-isFeatured">Mark as Featured Product</Label>
              </div>
              {/* Pricing */}
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="edit-price">Base Pricing</Label>
                  <div className="relative">
                    <span className="absolute left-3 top-1/2 -translate-y-1/2 text-gray-500">$</span>
                    <Input
                      id="edit-price"
                      name="price"
                      type="number"
                      placeholder="0.00"
                      value={editForm.price}
                      onChange={handleEditChange}
                      className="pl-8 bg-gray-100"
                      required
                    />
                  </div>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="edit-promotionalPricePercent">Promotional %</Label>
                  <Input
                    id="edit-promotionalPricePercent"
                    name="promotionalPricePercent"
                    type="number"
                    step="0.01"
                    placeholder="0.00"
                    value={editForm.promotionalPricePercent}
                    onChange={handleEditChange}
                    className="bg-gray-100"
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="edit-salesPricePercent">Sales %</Label>
                  <Input
                    id="edit-salesPricePercent"
                    name="salesPricePercent"
                    type="number"
                    step="0.01"
                    placeholder="0.00"
                    value={editForm.salesPricePercent}
                    onChange={handleEditChange}
                    className="bg-gray-100"
                  />
                </div>
              </div>
              {/* Category & Subcategory */}
              <div className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="edit-category">Product Category</Label>
                  <select
                    id="edit-category"
                    name="categoryId"
                    value={editForm.categoryId}
                    onChange={handleEditChange}
                    className="flex h-10 w-full rounded-md border border-gray-200 bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-violet-500 focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                  >
                    <option value="">Select a category</option>
                    {categories.map((category) => (
                      <option key={category.id} value={String(category.id)}>
                        {category.name}
                      </option>
                    ))}
                  </select>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="edit-subcategory">Product Subcategory</Label>
                  <select
                    id="edit-subcategory"
                    name="subcategoryId"
                    value={
                      subcategories.find(sc => String(sc.id) === String(editForm.subcategoryId))
                        ? editForm.subcategoryId
                        : ""
                    }
                    onChange={handleEditChange}
                    className="flex h-10 w-full rounded-md border border-gray-200 bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-violet-500 focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                  >
                    <option value="">Select a subcategory</option>
                    {subcategories.map((subcategory) => (
                      <option key={subcategory.id} value={String(subcategory.id)}>
                        {subcategory.name}
                      </option>
                    ))}
                  </select>
                </div>
              </div>
            </div>
            <DialogFooter className="gap-2 sm:gap-0">
              <Button
                type="button"
                variant="outline"
                onClick={() => setIsEditDialogOpen(false)}
                className="border-gray-200"
              >
                Cancel
              </Button>
              <Button type="submit" disabled={isSubmitting} className="bg-violet-600 hover:bg-violet-700 text-white">
                {isSubmitting ? (
                  <span className="flex items-center gap-1">
                    <span className="h-4 w-4 border-2 border-white border-t-transparent rounded-full animate-spin"></span>
                    Saving...
                  </span>
                ) : (
                  <span className="flex items-center gap-1">
                    <Check className="h-4 w-4" />
                    Save Changes
                  </span>
                )}
              </Button>
            </DialogFooter>
          </form>
        </DialogContent>
      </Dialog>

      {/* Delete Product Confirmation */}
      {productToDelete && (
        <>
          <AlertDialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
            <AlertDialogContent>
              <AlertDialogHeader>
                <AlertDialogTitle>Delete Product</AlertDialogTitle>
                <AlertDialogDescription>
                  Are you sure you want to delete <span className="font-medium text-gray-900">
                    {productToDelete?.title?.[0] || "this product"}
                  </span>? This action cannot be undone.
                </AlertDialogDescription>
              </AlertDialogHeader>
              <AlertDialogFooter className="mt-6 flex flex-col-reverse sm:flex-row gap-2">
                <AlertDialogCancel
                  onClick={() => setIsDeleteDialogOpen(false)}
                  className="w-full sm:w-auto px-6 border-gray-200 rounded-lg font-medium text-gray-700 bg-white hover:bg-gray-50 transition justify-center"
                >
                  Cancel
                </AlertDialogCancel>
                <AlertDialogAction
                  className="w-full sm:w-auto px-6 bg-gradient-to-r from-red-600 to-pink-600 hover:from-red-700 hover:to-pink-700 text-white font-semibold rounded-lg shadow transition justify-center"
                  onClick={confirmDeleteProduct}
                  disabled={isSubmitting}
                >
                  Delete
                </AlertDialogAction>
              </AlertDialogFooter>
            </AlertDialogContent>
          </AlertDialog>
        </>
      )}

      {/* Floating Action Button for Mobile */}
      <div className="fixed bottom-6 right-6 md:hidden">
        <Button
          asChild
          size="lg"
          className="h-14 w-14 rounded-full shadow-lg bg-gradient-to-r from-violet-600 to-indigo-600 hover:from-violet-700 hover:to-indigo-700"
        >
          <Link prefetch={true} href="/admin/add-product">
            <Plus className="h-6 w-6" />
            <span className="sr-only">Add New Product</span>
          </Link>
        </Button>
      </div>
    </div> 
  );
}

export default function Page() {
  return (
    <Suspense fallback={<div>Loading...</div>}>
      <ManageProductsPage />
    </Suspense>
  );
}