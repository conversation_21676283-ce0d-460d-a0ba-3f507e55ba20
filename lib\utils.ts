import { type ClassValue, clsx } from 'clsx'
import { twMerge } from 'tailwind-merge'
import { put } from '@vercel/blob'

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}

export function formatDate(date: Date): string {
  return new Date(date).toLocaleDateString('en-US', {
    month: 'long',
    day: 'numeric',
    year: 'numeric',
  });
}

export async function uploadBase64ImageToBlob(base64Image: string, fileName: string): Promise<string> {
  try {
    // Extract the base64 data from the data URL
    const base64Data = base64Image.split(',')[1];
    
    // Convert base64 to buffer
    const buffer = Buffer.from(base64Data, 'base64');
    
    // Create a Blob from the buffer
    const blob = new Blob([buffer]);
    
    // Upload to Vercel Blob
    const { url } = await put(fileName, blob, {
      access: 'public',
    });
    
    return url;
  } catch (error) {
    console.error('Error uploading image to Vercel Blob:', error);
    throw new Error('Failed to upload image');
  }
}
