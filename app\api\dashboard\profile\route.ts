import { NextRequest, NextResponse } from "next/server";
import db from "@/lib/db"; // Your Neon db client
import { auth } from "@/lib/auth"; // Your NextAuth or custom auth logic

export async function GET() {
  try {
    const session = await auth();

    if (!session?.user?.id) {
      return NextResponse.json({ success: false, error: "Unauthorized" }, { status: 401 });
    }

    const result = await db.query(
      `SELECT id, name, email, phone, street, city, province, postal_code, country
       FROM users WHERE id = $1`,
      [session.user.id]
    );

    if (result.rowCount === 0) {
      return NextResponse.json({ success: false, error: "User not found" }, { status: 404 });
    }

    // Add cache-control header for 1 hour (3600 seconds)
    const response = NextResponse.json({ success: true, data: result.rows[0] });
    response.headers.set('Cache-Control', 'public, s-maxage=3600, stale-while-revalidate=86400');
    
    return response;
  } catch (error) {
    console.error("GET /api/dashboard/profile error:", error);
    return NextResponse.json({ success: false, error: "Failed to fetch profile" }, { status: 500 });
  }
}

export async function PUT(request: NextRequest) {
  try {
    const session = await auth();

    if (!session?.user?.id) {
      return NextResponse.json({ success: false, error: "Unauthorized" }, { status: 401 });
    }

    const body = await request.json();
    const { name, email, phone, street, city, province, postal_code, country } = body;

    const result = await db.query(
      `UPDATE users SET
        name = $1,
        email = $2,
        phone = $3,
        street = $4,
        city = $5,
        province = $6,
        postal_code = $7,
        country = $8,
        updated_at = NOW()
       WHERE id = $9
       RETURNING id, name, email, phone, street, city, province, postal_code, country`,
      [name, email, phone, street, city, province, postal_code, country, session.user.id]
    );

    if (result.rowCount === 0) {
      return NextResponse.json({ success: false, error: "User not found" }, { status: 404 });
    }

    return NextResponse.json({ success: true, data: result.rows[0] });
  } catch (error) {
    console.error("PUT /api/dashboard/profile error:", error);
    return NextResponse.json({ success: false, error: "Failed to update profile" }, { status: 500 });
  }
}
