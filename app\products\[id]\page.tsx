import { safeQuery, isApplicationShuttingDown } from "@/lib/db";
import ProductDetail from './productDetail';
import { notFound } from "next/navigation";

type DBProduct = {
  id: number;
  title: string;
  short_description: string;
  long_description: string | string[];
  price: string | number;
  image_url: string;
  gallery_urls: string[];
  subcategory_id?: number;
  created_at?: string;
};

async function getProduct(id: string): Promise<DBProduct | null> {
  // Check if application is shutting down
  if (isApplicationShuttingDown()) {
    console.warn("Application is shutting down, skipping database query");
    return null;
  }

  try {
    // Use safeQuery which tracks active requests and handles shutdown gracefully
    const result = await safeQuery<DBProduct>(
      "SELECT * FROM product WHERE id = $1",
      [id]
    );
    return result.rows[0] || null;
  } catch (error) {
    console.error("Error fetching product:", error);

    // Check if it's a connection error
    if (error && typeof error === 'object' && 'code' in error) {
      const pgError = error as { code: string; message: string };
      if (pgError.code === '53300') {
        console.error("Database connection limit reached. This indicates too many concurrent connections.");
        console.error("Current pool settings: max 5 connections. Consider:");
        console.error("1. Reducing concurrent requests");
        console.error("2. Using connection pooling (PgBouncer)");
        console.error("3. Checking for connection leaks");
      }
    }

    return null;
  }
}

export async function generateMetadata({ params }: { params: Promise<{ id: string }> }) {
  const { id } = await params;
  const product = await getProduct(id);

  if (!product) {
    return {
      title: 'Product Not Found',
    };
  }

  const title = product.title;
  const description = product.short_description;

  return {
    title,
    description,
  };
}

export default async function ProductPage({ params }: { params: Promise<{ id: string }> }) {
  const { id } = await params;
  const product = await getProduct(id);

  if (!product) {
    notFound();
  }

  return (
    <div className="bg-white w-full py-8">
      <ProductDetail product={product} />
    </div>
  );
}

