import { getPool } from "@/lib/db"
import { NextResponse } from "next/server"

export async function GET(
  request: Request,
  { params }: { params: Promise<{ id: string }> }
) {
  const { id } = await params
  const pool = getPool();
  try {
    const result = await pool.query(
      `SELECT 
        id, 
        title,
        short_description,
        long_description,
        price,
        promotional_price_percent,
        sales_price_percent,
        image_url,
        gallery_urls,
        subcategory_id,     
        is_featured  
      FROM product 
      WHERE id = $1`,
      [id]
    )

    if (result.rows.length === 0) {
      return NextResponse.json({ error: "Product not found" }, { status: 404 })
    }

    return NextResponse.json(result.rows[0])
  } catch (error) {
    console.error("Fetch product error:", error)
    return NextResponse.json({ error: "Failed to fetch product" }, { status: 500 })
  }
}

export async function PUT(
  request: Request,
  { params }: { params: Promise<{ id: string }> }
) {
  const { id } = await params
  const body = await request.json()
  const pool = getPool();
  try {
    const result = await pool.query(
      `UPDATE product SET 
        title = $1,
        short_description = $2,
        long_description = $3,
        price = $4,
        promotional_price_percent = $5,
        sales_price_percent = $6,
        image_url = $7,
        gallery_urls = $8,
        subcategory_id = $9,
        is_featured = $10
      WHERE id = $11
      RETURNING *`,
      [
        body.title,
        body.short_description,
        body.long_description,
        body.price,
        body.promotional_price_percent,
        body.sales_price_percent,
        body.image_url,
        body.gallery_urls,
        body.subcategory_id,
        body.is_featured,
        id,
      ]
    )
    if (result.rows.length === 0) {
      return NextResponse.json({ error: "Product not found" }, { status: 404 })
    }
    return NextResponse.json(result.rows[0])
  } catch (error) {
    console.error("Update product error:", error)
    return NextResponse.json({ error: "Failed to update product" }, { status: 500 })
  }
}

export async function DELETE(
  request: Request,
  { params }: { params: Promise<{ id: string }> }
) {
  const { id } = await params
  const pool = getPool();
  try {
    const result = await pool.query(
      "DELETE FROM product WHERE id = $1 RETURNING *",
      [id]
    )
    if (result.rows.length === 0) {
      return NextResponse.json({ error: "Product not found" }, { status: 404 })
    }
    return NextResponse.json({ success: true })
  } catch (error) {
    console.error("Delete product error:", error)
    return NextResponse.json({ error: "Failed to delete product" }, { status: 500 })
  }
}