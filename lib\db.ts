import { Pool } from "pg"

let pool: Pool | null = null;

process.env.NODE_TLS_REJECT_UNAUTHORIZED = '0';

// Function to log connection statistics
async function logConnectionStats() {
  if (!pool) return;

  try {
    console.log(`Pool stats - Total: ${pool.totalCount}, Idle: ${pool.idleCount}, Waiting: ${pool.waitingCount}`);
  } catch (error) {
    console.error('Error getting pool stats:', error);
  }
}

export function getPool(): Pool {
  // Prevent new connections during shutdown
  if (isShuttingDown) {
    throw new Error('Application is shutting down, cannot create new database connections');
  }

  if (!pool) {
     pool = new Pool({
      connectionString: process.env.DATABASE_URL,
      ssl: {
        rejectUnauthorized: false,
      },
      // Conservative connection pool settings to prevent exhaustion
      max: 5, // Reduced from 20 - most databases have limited connections
      idleTimeoutMillis: 10000, // Close idle clients after 10 seconds (reduced)
      connectionTimeoutMillis: 3000, // Faster timeout to fail fast
      allowExitOnIdle: false, // Keep pool alive to prevent recreation
    })

    pool.on('error', (err: Error) => {
      console.error('Unexpected database pool error:', err);
      // Log connection stats for debugging
      logConnectionStats();
    });

    pool.on('connect', (client) => {
      console.log('New database client connected');
      // Set session-level optimizations with shorter timeouts
      client.query(`
        SET statement_timeout = '15s';
        SET lock_timeout = '5s';
        SET idle_in_transaction_session_timeout = '30s';
      `).catch(err => console.error('Error setting session parameters:', err));
    });

    pool.on('remove', () => {
      console.log('Database client removed from pool');
    });

    pool.on('acquire', () => {
      console.log('Client acquired from pool');
    });

    pool.on('release', () => {
      console.log('Client released back to pool');
    });
  }
  return pool;
}

// Enhanced shutdown handling with request tracking
let activeRequests = 0;
let isShuttingDown = false;

export function incrementActiveRequests() {
  activeRequests++;
}

export function decrementActiveRequests() {
  activeRequests = Math.max(0, activeRequests - 1);
}

export function isApplicationShuttingDown() {
  return isShuttingDown;
}

if (typeof process !== 'undefined') {
  const gracefulShutdown = async () => {
    if (isShuttingDown) return; // Prevent multiple shutdown attempts

    isShuttingDown = true;
    console.log('Starting graceful shutdown...');

    // Wait for active requests to complete (max 10 seconds)
    const maxWaitTime = 10000;
    const startTime = Date.now();

    while (activeRequests > 0 && (Date.now() - startTime) < maxWaitTime) {
      console.log(`Waiting for ${activeRequests} active requests to complete...`);
      await new Promise(resolve => setTimeout(resolve, 100));
    }

    if (pool) {
      console.log('Closing database pool due to application shutdown');
      try {
        await logConnectionStats();
        await pool.end();
        console.log('Database pool closed successfully');
        pool = null;
      } catch (error) {
        console.error('Error closing database pool:', error);
      }
    }
  };

  process.on('SIGINT', gracefulShutdown);
  process.on('SIGTERM', gracefulShutdown);
  // Remove beforeExit as it can cause premature shutdowns
}

/**
 * Safe database query wrapper that tracks active requests
 */
export async function safeQuery<T = unknown>(
  text: string,
  params?: unknown[]
): Promise<{ rows: T[]; rowCount: number }> {
  incrementActiveRequests();

  try {
    const pool = getPool();
    const result = await pool.query(text, params);
    return {
      rows: result.rows,
      rowCount: result.rowCount || 0
    };
  } finally {
    decrementActiveRequests();
  }
}

const defaultPool = getPool();
export default defaultPool;
