import { NextResponse, type NextRequest } from "next/server";
import db from "@/lib/db";
import { auth } from "@/lib/auth";

// Enum-like array for allowed statuses (must match DB exactly)
const ALLOWED_STATUSES = ["PENDING", "PROCESSING", "COMPLETED", "CANCELLED"] as const;

// Type helper for allowed statuses
type AllowedStatus = (typeof ALLOWED_STATUSES)[number];

// Type guard to check if status is valid
function isAllowedStatus(value: string): value is AllowedStatus {
  return ALLOWED_STATUSES.includes(value as AllowedStatus);
}

export async function PATCH(
  request: NextRequest,
  context: { params: Promise<{ id: string }> }
): Promise<NextResponse> {
  try {
    const session = await auth();

    if (!session?.user?.id || session.user.userType !== "admin") {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { id: orderId } = await context.params;

    const body = await request.json();

    const rawStatus = body.status;
    const status =
      typeof rawStatus === "string" ? rawStatus.trim().toUpperCase() : null;

    if (!status || !isAllowedStatus(status)) {
      return NextResponse.json(
        {
          error: "Invalid status",
          detail: `Status must be one of: ${ALLOWED_STATUSES.join(", ")}`,
          allowedStatuses: ALLOWED_STATUSES,
        },
        { status: 400 }
      );
    }

    const result = await db.query(
      `UPDATE orders SET status = $1, updated_at = NOW() WHERE id = $2 RETURNING *`,
      [status, orderId]
    );

    if (result.rowCount === 0) {
      return NextResponse.json({ error: "Order not found" }, { status: 404 });
    }

    return NextResponse.json({
      success: true,
      data: result.rows[0],
    });
  } catch (error) {
    console.error("[ORDER_UPDATE_ERROR]", error);
    return NextResponse.json({ error: "Internal server error" }, { status: 500 });
  }
}
