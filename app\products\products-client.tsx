"use client";

import Image from "next/image";
import Link from "next/link";
import { useEffect, useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Eye } from "lucide-react";
import { ProductsGridSkeleton } from "@/components/ProductSkeleton";
import {
  Sheet,
  SheetContent,
  SheetDescription,
  SheetHeader,
  SheetTitle,
  <PERSON>et<PERSON>rigger,
  <PERSON>et<PERSON>ooter,
  SheetClose,
} from "@/components/ui/sheet";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import {
  RadioGroup,
  RadioGroupItem,
} from "@/components/ui/radio-group";
import { Label } from "@/components/ui/label";
import { useSearchParams, useRouter } from "next/navigation";

interface Product {
  id: string;
  title: string[];
  short_description: string[];
  long_description: string[];
  price: number;
  promotional_price_percent?: number;
  sales_price_percent?: number;
  image_url: string;
  gallery_urls?: string[];
  created_at?: string;
  category_id?: string;
  category_name?: string;
}

interface Category {
  id: string;
  name: string;
}

interface ProductsClientProps {
  initialProducts?: Product[];
  initialCategories?: Category[];
}

export default function ProductsClient({ initialProducts, initialCategories }: ProductsClientProps) {
  const [products, setProducts] = useState<Product[]>(initialProducts || []);
  const [categories, setCategories] = useState<Category[]>(initialCategories || []);
  const [loading, setLoading] = useState(!initialProducts);
  const [isSheetOpen, setIsSheetOpen] = useState(false);
  
  const router = useRouter();
  const searchParams = useSearchParams();
  
  const selectedCategory = searchParams.get("category") || "";
  const sortBy = searchParams.get("sort") || "newest";
  
  useEffect(() => {
    const fetchInitialData = async () => {
      let fetchedCategories = initialCategories || [];
      if (!initialCategories || initialCategories.length === 0) {
        setLoading(true);
        try {
          const catRes = await fetch("/api/categories");
          if (catRes.ok) {
            const catData = await catRes.json();
            fetchedCategories = catData.categories || [];
            setCategories(fetchedCategories);
          }
        } catch (error) {
          console.error("Error fetching categories:", error);
        }
      }

      if (!initialProducts || initialProducts.length === 0) {
        setLoading(true);
        await fetchProducts(selectedCategory, sortBy);
      } else {
        const currentParamsString = searchParams.toString();
        
        if (initialProducts && initialProducts.length > 0 && currentParamsString === "") {
             setProducts(initialProducts);
             setLoading(false);
        } else {
            await fetchProducts(selectedCategory, sortBy);
        }
      }
    };

    fetchInitialData();
  }, [selectedCategory, sortBy, initialProducts, initialCategories, searchParams]);
  
  const fetchProducts = async (categoryId?: string | null, sort?: string | null) => {
    setLoading(true);
    try {
      let url = "/api/products";
      const params = new URLSearchParams();
      
      if (categoryId) {
        params.append("category", categoryId);
      }
      
      if (sort) {
        params.append("sort", sort);
      }
      
      if (params.toString()) {
        url += `?${params.toString()}`;
      }
      
      const res = await fetch(url);
      if (!res.ok) {
        throw new Error("Failed to fetch products");
      }
      
      const data = await res.json();
      setProducts(data.products || []);
    } catch (error) {
      console.error("Error fetching products:", error);
    } finally {
      setLoading(false);
    }
  };
  
  const handleCategoryChange = (categoryId: string) => {
    const params = new URLSearchParams(searchParams.toString());
    if (categoryId) {
      params.set("category", categoryId);
    } else {
      params.delete("category");
    }
    router.push(`/products${params.toString() ? `?${params.toString()}` : ''}`);
  };
  
  const handleSortChange = (value: string) => {
    const params = new URLSearchParams(searchParams.toString());
    if (value && value !== "newest") {
      params.set("sort", value);
    } else {
      params.delete("sort");
    }
    router.push(`/products${params.toString() ? `?${params.toString()}` : ''}`);
  };
  
  const resetFilters = () => {
    router.push("/products");
  };

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="flex justify-between items-center mb-8">
        <h1 className="text-3xl font-bold">Our Products</h1>
        <div className="flex gap-4">
          <Sheet onOpenChange={setIsSheetOpen}>
            <SheetTrigger asChild>
              <Button variant="outline">Filter</Button>
            </SheetTrigger>
            <SheetContent>
              <SheetHeader>
                <SheetTitle>Filter Products</SheetTitle>
                <SheetDescription>
                  Filter products by category
                </SheetDescription>
              </SheetHeader>
              <div className="py-6">
                <RadioGroup
                  key={`${isSheetOpen}-${selectedCategory}`}
                  value={selectedCategory}
                  onValueChange={handleCategoryChange}
                >
                  <div className="space-y-2">
                    <div className="flex items-center space-x-2">
                      <RadioGroupItem value="" id="all" />
                      <Label htmlFor="all">All Categories</Label>
                    </div>
                    {categories.map((category) => (
                      <div key={category.id} className="flex items-center space-x-2">
                        <RadioGroupItem value={String(category.id)} id={String(category.id)} />
                        <Label htmlFor={String(category.id)}>{category.name}</Label>
                      </div>
                    ))}
                  </div>
                </RadioGroup>
              </div>
              <SheetFooter>
                <SheetClose asChild>
                  <Button variant="outline" onClick={resetFilters}>Reset</Button>
                </SheetClose>
              </SheetFooter>
            </SheetContent>
          </Sheet>
          
          <Popover>
            <PopoverTrigger asChild>
              <Button variant="outline">Sort</Button>
            </PopoverTrigger>
            <PopoverContent className="w-56">
              <div className="space-y-2">
                <h4 className="font-medium">Sort by</h4>
                <RadioGroup value={sortBy} onValueChange={handleSortChange}>
                  <div className="space-y-2">
                    <div className="flex items-center space-x-2">
                      <RadioGroupItem value="newest" id="newest" />
                      <Label htmlFor="newest">Newest</Label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <RadioGroupItem value="price_asc" id="price_asc" />
                      <Label htmlFor="price_asc">Price: Low to High</Label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <RadioGroupItem value="price_desc" id="price_desc" />
                      <Label htmlFor="price_desc">Price: High to Low</Label>
                    </div>
                  </div>
                </RadioGroup>
              </div>
            </PopoverContent>
          </Popover>
        </div>
      </div>

      {loading ? (
        <ProductsGridSkeleton />
      ) : (
        <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4 xl:gap-6">
          {products.length === 0 ? (
            <div className="col-span-full text-center py-12">
              <h3 className="text-xl font-medium text-gray-600">No products found</h3>
              <p className="mt-2 text-gray-500">Try adjusting your filters or check back later.</p>
            </div>
          ) : (
            products.map((product: Product) => (
              <Card key={product.id} className="overflow-hidden printcloud-card-hover h-full flex flex-col">
                <div className="relative aspect-square w-full">
                  <Link href={`/products/${product.id}`} prefetch={true}>
                    <Image
                      src={product.image_url && product.image_url.trim() !== "" ? product.image_url : "/placeholder.svg"}
                      alt={Array.isArray(product.title) && product.title.length > 0 ? product.title[0] : "Product"}
                      fill
                      sizes="(max-width: 640px) 100vw, (max-width: 768px) 50vw, (max-width: 1024px) 33vw, 25vw"
                      className="object-cover cursor-pointer transition-transform duration-300 hover:scale-105"
                    />
                  </Link>
                </div>
                <CardContent className="p-4 flex flex-col flex-grow">
                  <h3 className="font-semibold text-lg mb-1 line-clamp-1">
                    {Array.isArray(product.title) && product.title.length > 0 ? product.title[0] : "Product"}
                  </h3>
                  <p className="text-gray-500 text-sm mb-2 line-clamp-2">
                    {Array.isArray(product.short_description) && product.short_description.length > 0 ? product.short_description[0] : ""}
                  </p>
                  {product.category_name && (
                    <p className="text-xs text-blue-600 mb-2">
                      {product.category_name}
                    </p>
                  )}
                  <div className="flex items-center justify-between mt-auto">
                    <p className="font-bold text-blue-600">
                      ${Number(product.price).toFixed(2)} CAD
                    </p>
                    <Button asChild className="bg-blue-600 hover:bg-blue-700 ml-2">
                      <Link href={`/products/${product.id}`} prefetch={true} className="flex items-center gap-2">
                        <Eye className="h-4 w-4" />
                        View Product
                      </Link>
                    </Button>
                  </div>
                </CardContent>
              </Card>
            ))
          )}
        </div>
      )}
    </div>
  );
}