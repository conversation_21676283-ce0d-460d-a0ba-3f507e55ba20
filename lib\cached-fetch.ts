import { cache } from 'react'

// Cache orders data for 1 hour (3600 seconds)
export const fetchOrders = cache(async () => {
  const response = await fetch('/api/dashboard/orders', {
    method: 'GET',
    headers: { 'Content-Type': 'application/json' },
    credentials: 'include',
    next: { 
      revalidate: 3600,
      tags: ['dashboard-data', 'orders-data']
    }
  })

  if (!response.ok) {
    const errorText = await response.text()
    console.error("API Response Error:", response.status, errorText)
    throw new Error(`HTTP ${response.status}: ${errorText || "Failed to fetch orders"}`)
  }

  const data = await response.json()
  return data
})

// Cache user profile data for 1 hour
export const fetchUserProfile = cache(async () => {
  const response = await fetch('/api/dashboard/profile', {
    method: 'GET',
    headers: { 'Content-Type': 'application/json' },
    credentials: 'include',
    next: { 
      revalidate: 3600,
      tags: ['dashboard-data', 'profile-data']
    }
  })

  if (!response.ok) {
    throw new Error('Failed to fetch profile')
  }

  const data = await response.json()
  return data
}) 